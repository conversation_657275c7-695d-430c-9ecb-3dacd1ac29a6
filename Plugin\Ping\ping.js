// Plugin/Ping/ping.js

/**
 * 一个使用 direct-in-process 协议的超轻量级插件。
 * @param {object} args - 从AI调用传递过来的参数对象 (在这个例子中我们不需要它)。
 * @param {object} config - 从插件清单和全局配置解析出的配置对象 (在这个例子中我们不需要它)。
 * @returns {Promise<object>} 一个解析为包含成功状态和结果的对象的Promise。
 */
async function execute(args, config) {
    // 对于进程内插件，我们直接返回一个符合预期的对象即可。
    // PluginManager 会处理时间戳和MaidName的添加。
    return {
        status: "success",
        result: "pong"
    };
}

module.exports = {
    execute
};