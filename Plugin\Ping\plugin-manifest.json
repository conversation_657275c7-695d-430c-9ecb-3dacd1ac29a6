{"name": "<PERSON>", "displayName": "网络延迟测试", "version": "1.0.0", "description": "一个用于测试服务器响应延迟的超轻量级插件。它使用新的 'direct-in-process' 协议，以实现极致的性能。", "pluginType": "synchronous", "entryPoint": {"script": "ping.js"}, "communication": {"protocol": "direct-in-process"}, "capabilities": {"invocationCommands": [{"command": "ping", "description": "调用此工具来检查服务器是否在线并测量响应时间。它会立即返回 'pong'。", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」Ping「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}