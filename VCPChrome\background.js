console.log('VCPChrome background.js loaded.');
let ws = null;
let isConnected = false;
let reconnectTimer = null;
let reconnectAttempts = 0;
let manualDisconnect = false; // 标记是否为手动断开
let isReconnecting = false; // 标记是否正在重连
let heartbeatTimer = null; // 心跳定时器
let lastMessageSentTime = null;
let lastMessageReceivedTime = null;
const maxReconnectAttempts = 30; // 增加最大重连次数
const reconnectDelay = 3000; // 3秒
const heartbeatInterval = 30000; // 30秒心跳间隔
const defaultServerUrl = 'ws://localhost:6005'; // 默认服务器地址
const defaultVcpKey = '030213'; // 默认密钥

function connect() {
    if (ws && ws.readyState === WebSocket.OPEN) {
        console.log('WebSocket is already connected.');
        return;
    }

    // 从storage获取URL和Key
    chrome.storage.local.get(['serverUrl', 'vcpKey'], (result) => {
        const serverUrlToUse = result.serverUrl || defaultServerUrl;
        const keyToUse = result.vcpKey || defaultVcpKey;
        
        const fullUrl = `${serverUrlToUse}/vcp-chrome-observer/VCP_Key=${keyToUse}`;
        console.log('Connecting to:', fullUrl);

        ws = new WebSocket(fullUrl);

        ws.onopen = () => {
            console.log('WebSocket connection established.');
            console.log('Connection details:', {
                readyState: ws.readyState,
                url: ws.url,
                protocol: ws.protocol,
                extensions: ws.extensions
            });
            isConnected = true;
            reconnectAttempts = 0; // 重置重连计数器
            manualDisconnect = false; // 重置手动断开标记
            isReconnecting = false; // 重置重连标记
            if (reconnectTimer) {
                clearTimeout(reconnectTimer);
                reconnectTimer = null;
            }
            // 启动心跳机制
            startHeartbeat();
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态
        };

        ws.onmessage = (event) => {
            lastMessageReceivedTime = new Date();
            console.log('Message from server:', event.data);
            const message = JSON.parse(event.data);
            
            // 处理来自服务器的指令
            if (message.type === 'command') {
                const commandData = message.data;
                console.log('Received commandData:', commandData);
                // 检查是否是 open_url 指令
                if (commandData.command === 'open_url' && commandData.url) {
                    console.log('Handling open_url command. URL:', commandData.url);
                    let fullUrl = commandData.url;
                    if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
                        fullUrl = 'https://' + fullUrl;
                    }
                    console.log('Attempting to create tab with URL:', fullUrl);
                    chrome.tabs.create({ url: fullUrl }, (tab) => {
                        if (chrome.runtime.lastError) {
                            const errorMessage = `创建标签页失败: ${chrome.runtime.lastError.message}`;
                            console.error('Error creating tab:', errorMessage);
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: 'command_result',
                                    data: {
                                        requestId: commandData.requestId,
                                        status: 'error',
                                        error: errorMessage
                                    }
                                }));
                            }
                        } else {
                            console.log('Tab created successfully. Tab ID:', tab.id, 'URL:', tab.url);
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: 'command_result',
                                    data: {
                                        requestId: commandData.requestId,
                                        sourceClientId: commandData.sourceClientId, // 确保返回 sourceClientId
                                        status: 'success',
                                        message: `成功打开URL: ${commandData.url}`
                                    }
                                }));
                            }
                        }
                    });
                } else {
                    console.log('Forwarding command to content script:', commandData);
                    forwardCommandToContentScript(commandData);
                }
            }
        };

        ws.onclose = (event) => {
            console.log('WebSocket connection closed.');
            console.log('Close details:', {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean,
                timestamp: new Date().toISOString()
            });
            isConnected = false;
            ws = null;
            // 停止心跳
            stopHeartbeat();
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态
            scheduleReconnect();
        };

        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            console.error('Error details:', {
                type: error.type,
                target: error.target,
                timestamp: new Date().toISOString()
            });
            isConnected = false;
            ws = null;
            // 停止心跳
            stopHeartbeat();
            updateIcon();
            broadcastStatusUpdate(); // 广播最新状态
            scheduleReconnect();
        };
    });
}

function disconnect() {
    // 标记为手动断开，防止自动重连
    manualDisconnect = true;
    // 清除重连定时器
    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
    }
    // 停止心跳
    stopHeartbeat();
    if (ws) {
        ws.close();
    }
}

function updateIcon() {
    const iconPath = isConnected ? 'icons/icon48.png' : 'icons/icon_disconnected.png'; // 你需要创建一个断开连接的图标
    // 为了简单起见，我们先只改变徽章
    chrome.action.setBadgeText({ text: isConnected ? 'On' : 'Off' });
    chrome.action.setBadgeBackgroundColor({ color: isConnected ? '#00C853' : '#FF5252' });
}

// 监听来自popup和content_script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'GET_DETAILED_STATUS') {
        chrome.storage.local.get(['serverUrl'], (result) => {
            sendResponse({
                isConnected: isConnected,
                isReconnecting: isReconnecting,
                serverUrl: result.serverUrl || defaultServerUrl,
                lastSent: lastMessageSentTime ? lastMessageSentTime.toLocaleTimeString() : 'N/A',
                lastReceived: lastMessageReceivedTime ? lastMessageReceivedTime.toLocaleTimeString() : 'N/A'
            });
        });
    } else if (request.type === 'TOGGLE_CONNECTION') {
        if (isConnected) {
            disconnect();
        } else {
            // 手动连接时重置标记，允许后续自动重连
            manualDisconnect = false;
            reconnectAttempts = 0;
            connect();
        }
        // 不再立即返回状态，而是等待广播
        // sendResponse({ isConnected: !isConnected });
    } else if (request.type === 'PAGE_INFO_UPDATE') {
        // 从content_script接收到页面信息，发送到服务器
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'pageInfoUpdate',
                data: { markdown: request.data.markdown }
            }));
            lastMessageSentTime = new Date();
        }
    } else if (request.type === 'COMMAND_RESULT') {
        // 从content_script接收到命令执行结果，发送到服务器
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
                type: 'command_result',
                data: request.data
            }));
            lastMessageSentTime = new Date();
        }
    }
    return true; // 保持消息通道开放以进行异步响应
});

function forwardCommandToContentScript(commandData) {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
                type: 'EXECUTE_COMMAND',
                data: commandData
            });
        }
    });
}

function broadcastStatusUpdate() {
    chrome.runtime.sendMessage({
        type: 'STATUS_UPDATE',
        isConnected: isConnected
    }).catch(error => {
        // 捕获当popup未打开时发送消息产生的错误，这是正常现象
        if (error.message.includes("Could not establish connection. Receiving end does not exist.")) {
            // This is expected if the popup is not open.
        } else {
            console.error("Error broadcasting status:", error);
        }
    });
}

// 监听标签页切换
chrome.tabs.onActivated.addListener((activeInfo) => {
    // 请求新激活的标签页更新信息
    chrome.tabs.sendMessage(activeInfo.tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {
        if (!e.message.includes("Could not establish connection")) console.log("Error sending to content script on tab activation:", e.message);
    });
});

// 监听标签页URL变化或加载状态变化
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当导航开始时，清除内容脚本的状态以防止内容累积
    if (changeInfo.status === 'loading') {
        chrome.tabs.sendMessage(tabId, { type: 'CLEAR_STATE' }).catch(e => {
            // This error is expected if the content script hasn't been injected yet
            if (!e.message.includes("Could not establish connection")) console.log("Error sending CLEAR_STATE:", e.message);
        });
    }
    // 当页面加载完成时，或者URL变化后加载完成时，请求更新
    if (changeInfo.status === 'complete' && tab.active) {
        chrome.tabs.sendMessage(tabId, { type: 'REQUEST_PAGE_INFO_UPDATE' }).catch(e => {
            if (!e.message.includes("Could not establish connection")) console.log("Error sending to content script on tab update:", e.message);
        });
    }
});

function scheduleReconnect() {
    // 如果是手动断开，不进行自动重连
    if (manualDisconnect) {
        console.log('手动断开连接，不进行自动重连');
        return;
    }

    if (reconnectAttempts >= maxReconnectAttempts) {
        console.log('已达到最大重连次数，停止自动重连');
        return;
    }

    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
    }

    reconnectAttempts++;
    isReconnecting = true;
    broadcastStatusUpdate();

    // Exponential backoff with jitter
    const delay = Math.min(30000, (Math.pow(2, reconnectAttempts) * 1000));
    const jitter = Math.random() * 1000;
    const reconnectDelayWithJitter = delay + jitter;

    console.log(`计划在 ${reconnectDelayWithJitter.toFixed(0)}ms 后进行第 ${reconnectAttempts} 次重连尝试`);

    reconnectTimer = setTimeout(() => {
        console.log(`开始第 ${reconnectAttempts} 次重连尝试`);
        connect();
    }, reconnectDelayWithJitter);
}

function startHeartbeat() {
    // 清除现有的心跳定时器
    stopHeartbeat();

    console.log('启动心跳机制');
    heartbeatTimer = setInterval(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            console.log('发送心跳包');
            try {
                ws.send(JSON.stringify({
                    type: 'heartbeat',
                    timestamp: Date.now()
                }));
                lastMessageSentTime = new Date();
            } catch (error) {
                console.error('发送心跳包失败:', error);
                stopHeartbeat();
            }
        } else {
            console.log('WebSocket未连接，停止心跳');
            stopHeartbeat();
        }
    }, heartbeatInterval);

    // Chrome扩展保活机制
    keepServiceWorkerAlive();
}

function keepServiceWorkerAlive() {
    // 定期执行一些轻量级操作来保持Service Worker活跃
    setInterval(() => {
        chrome.storage.local.get(['keepAlive'], () => {
            // 这个操作会保持Service Worker活跃
            if (chrome.runtime.lastError) {
                console.log('Keep alive check failed:', chrome.runtime.lastError);
            }
        });
    }, 20000); // 每20秒执行一次
}

function stopHeartbeat() {
    if (heartbeatTimer) {
        console.log('停止心跳机制');
        clearInterval(heartbeatTimer);
        heartbeatTimer = null;
    }
}

// Chrome扩展生命周期管理
chrome.runtime.onStartup.addListener(() => {
    console.log('Chrome扩展启动');
});

chrome.runtime.onInstalled.addListener(() => {
    console.log('Chrome扩展安装/更新');
});

// 监听Service Worker的suspend/resume
chrome.runtime.onSuspend.addListener(() => {
    console.log('Service Worker即将挂起，保存状态');
    if (ws) {
        ws.close();
    }
});

chrome.runtime.onSuspendCanceled.addListener(() => {
    console.log('Service Worker挂起被取消');
});

// 初始化图标状态
updateIcon();